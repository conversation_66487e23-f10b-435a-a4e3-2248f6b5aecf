#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
基于事件驱动的启动控制

使用Launch事件系统实现复杂的启动依赖关系。
"""

import os
from pathlib import Path

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import (AppendEnvironmentVariable, ExecuteProcess, IncludeLaunchDescription,
                            SetEnvironmentVariable, DeclareLaunchArgument, TimerAction,
                            RegisterEventHandler, EmitEvent, LogInfo, OpaqueFunction)
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.event_handlers import OnProcessStart, OnExecutionComplete
from launch_ros.actions import Node
from launch_ros.events.lifecycle import ChangeState
from lifecycle_msgs.msg import Transition
from nav2_common.launch import RewrittenYaml


def create_sensor_ready_checker(context):
    """
    创建传感器就绪检查器
    这个函数会启动一个监听器来等待传感器监控节点的就绪信号
    """
    import rclpy
    from rclpy.node import Node
    from std_msgs.msg import Bool
    import threading
    import time

    class SensorReadyChecker(Node):
        def __init__(self):
            super().__init__('sensor_ready_checker')
            self.sensors_ready = False
            self.subscription = self.create_subscription(
                Bool, '/sensors_ready', self.sensor_ready_callback, 10)
            self.get_logger().info('Waiting for sensors to be ready...')

        def sensor_ready_callback(self, msg):
            if msg.data and not self.sensors_ready:
                self.sensors_ready = True
                self.get_logger().info('Sensors ready signal received!')
                # 这里可以触发配置事件
                # 但在Launch系统中，我们使用不同的方法

    # 在实际的Launch系统中，我们使用传感器监控节点的进程完成事件
    # 来触发配置，而不是这个检查器
    return []


def generate_launch_description():
    # 获取包目录和文件路径
    sim_dir = get_package_share_directory('nav2_minimal_tb3_sim')
    launch_dir = os.path.dirname(os.path.realpath(__file__))

    world_sdf_xacro = os.path.join(sim_dir, 'worlds', 'turtlebot3_house.sdf.xacro')
    robot_sdf = os.path.join(sim_dir, 'urdf', 'gz_waffle_gps.sdf.xacro')
    urdf = os.path.join(sim_dir, 'urdf', 'turtlebot3_waffle_gps.urdf')

    # 读取URDF
    with open(urdf, 'r') as infp:
        robot_description = infp.read()

    # 参数文件配置
    params_file = os.path.join(launch_dir, 'nav2_no_map_params.yaml')
    message_filter_params_file = os.path.join(launch_dir, 'message_filter_params.yaml')
    indoor_ekf_params_file = os.path.join(launch_dir, 'indoor_ekf_params.yaml')
    dual_ekf_params_file = os.path.join(launch_dir, 'dual_ekf_navsat_params.yaml')

    configured_params = RewrittenYaml(source_file=params_file, root_key='', param_rewrites='', convert_types=True)
    configured_message_filter_params = RewrittenYaml(source_file=message_filter_params_file, root_key='', param_rewrites='', convert_types=True)
    configured_indoor_ekf_params = RewrittenYaml(source_file=indoor_ekf_params_file, root_key='', param_rewrites='', convert_types=True)

    # 启动参数
    declare_use_sim_time_cmd = DeclareLaunchArgument('use_sim_time', default_value='True')
    use_sim_time = LaunchConfiguration('use_sim_time')

    # ============================================================================
    # 阶段1: 基础设施层
    # ============================================================================

    # Gazebo仿真器
    gazebo_server = ExecuteProcess(
        cmd=['gz', 'sim', '-r', '-s', world_sdf_xacro],
        output='screen',
        name='gazebo_server'
    )

    # 机器人生成 (当Gazebo启动后)
    robot_spawn = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(sim_dir, 'launch', 'spawn_tb3_gps.launch.py')),
        launch_arguments={
            'use_sim_time': 'True',
            'robot_sdf': robot_sdf,
            'x_pose': '1.17',
            'y_pose': '-1.5',
            'z_pose': '0.01',
        }.items(),
    )

    # 机器人状态发布器
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'use_sim_time': True, 'robot_description': robot_description}],
    )

    # ============================================================================
    # 阶段2: 传感器和定位基础
    # ============================================================================

    # Robot Localization包装器
    robot_localization_wrapper = Node(
        package='nav2_system_tests',
        executable='robot_localization_lifecycle_wrapper.py',
        name='robot_localization_lifecycle_wrapper',
        output='screen',
        parameters=[
            {'params_file': dual_ekf_params_file},
            {'use_sim_time': True}
        ],
    )

    # 地图服务器
    map_server = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[configured_params, configured_message_filter_params, {'use_sim_time': True}],
        remappings=[('map', 'map'), ('map_metadata', 'map_metadata')],
    )

    # ============================================================================
    # 阶段3: 主要定位
    # ============================================================================

    # AMCL定位
    amcl = Node(
        package='nav2_amcl',
        executable='amcl',
        name='amcl',
        output='screen',
        parameters=[configured_params, configured_message_filter_params, {'use_sim_time': True}],
        remappings=[('scan', 'scan'), ('map', 'map'), ('map_metadata', 'map_metadata')],
    )

    # 室内EKF包装器
    indoor_ekf_wrapper = Node(
        package='nav2_system_tests',
        executable='indoor_ekf_lifecycle_wrapper.py',
        name='indoor_ekf_lifecycle_wrapper',
        output='screen',
        parameters=[
            {'params_file': configured_indoor_ekf_params},
            {'use_sim_time': True}
        ],
    )

    # AMCL和室内EKF的生命周期管理器
    lifecycle_manager_amcl_indoor = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_amcl_indoor',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': False},  # 手动控制启动
            {'node_names': ['amcl', 'indoor_ekf_lifecycle_wrapper']}
        ]
    )

    # Robot Localization的生命周期管理器
    lifecycle_manager_robot_localization = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_robot_localization',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': True},  # 手动控制启动
            {'node_names': ['robot_localization_lifecycle_wrapper']}
        ]
    )

    # Map Server的生命周期管理器
    lifecycle_manager_map_server = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_map_server',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': True},  # 手动控制启动
            {'node_names': ['map_server']}
        ]
    )

    # 传感器监控节点 - 持续检查传感器状态
    sensor_monitor = Node(
        package='nav2_system_tests',
        executable='sensor_monitor.py',
        name='sensor_monitor',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'required_sensors': ['scan', 'imu', 'gps', 'odom']},  # 必需的传感器
            {'check_frequency': 2.0},  # 检查频率 Hz
            {'timeout_per_sensor': 3.0},  # 每个传感器的超时时间
        ]
    )

    # ============================================================================
    # 事件处理器 - 定义启动顺序
    # ============================================================================

    # 当Gazebo启动后，启动机器人相关组件
    start_robot_after_gazebo = RegisterEventHandler(
        OnProcessStart(
            target_action=gazebo_server,
            on_start=[
                LogInfo(msg="Gazebo started, launching robot components..."),
                TimerAction(
                    period=2.0,  # 等待2秒让Gazebo稳定
                    actions=[robot_spawn, robot_state_publisher]
                )
            ]
        )
    )

    # 当机器人生成后，同时启动所有定位组件（但不激活）
    start_localization_components_after_robot = RegisterEventHandler(
        OnExecutionComplete(
            target_action=robot_spawn,
            on_completion=[
                LogInfo(msg="Robot spawned, starting all localization components..."),
                # 立即启动所有定位相关节点和生命周期管理器
                robot_localization_wrapper,
                map_server,
                amcl,
                indoor_ekf_wrapper,
                # 启动三个独立的生命周期管理器
                lifecycle_manager_amcl_indoor,      # 管理AMCL和室内EKF
                lifecycle_manager_robot_localization, # 管理Robot Localization
                lifecycle_manager_map_server,       # 管理Map Server
                # 启动传感器监控节点
                sensor_monitor
            ]
        )
    )

    # ============================================================================
    # 传感器监控事件处理器 - 基于传感器状态配置生命周期管理器
    # ============================================================================

    # 当传感器监控节点启动后，开始监听传感器状态
    configure_managers_on_sensors_ready = RegisterEventHandler(
        OnProcessStart(
            target_action=sensor_monitor,
            on_start=[
                LogInfo(msg="Sensor monitor started, waiting for all sensors to be ready..."),
                # 这里会等待传感器监控节点发布传感器就绪事件
                # 传感器监控节点会持续检查所有必需传感器，只有全部可用时才发布就绪事件
            ]
        )
    )

    # 当传感器监控节点检测到所有传感器就绪时，它会自动退出
    # 我们可以使用OnExecutionComplete事件来捕获这个"完成"事件
    configure_managers_when_sensors_ready = RegisterEventHandler(
        OnExecutionComplete(
            target_action=sensor_monitor,
            on_completion=[
                LogInfo(msg="All sensors are ready! Configuring all lifecycle managers..."),
                # 使用ExecuteProcess调用ros2 service来启动lifecycle managers
                # command=0 表示完整启动（configure + activate）

                # 启动Map Server
                # TimerAction(
                #     period=1.0,  # 传感器就绪后1秒启动Map Server
                #     actions=[
                #         LogInfo(msg="Event-driven: Starting Map Server (configure + activate)..."),
                #         ExecuteProcess(
                #             cmd=['ros2', 'service', 'call', '/lifecycle_manager_map_server/manage_nodes',
                #                  'nav2_msgs/srv/ManageLifecycleNodes', '{command: 0}'],
                #             output='screen'
                #         )
                #     ]
                # ),

                # # 启动Robot Localization
                # TimerAction(
                #     period=5.0,  # Map Server启动后2秒启动Robot Localization
                #     actions=[
                #         LogInfo(msg="Event-driven: Starting Robot Localization (configure + activate)..."),
                #         ExecuteProcess(
                #             cmd=['ros2', 'service', 'call', '/lifecycle_manager_robot_localization/manage_nodes',
                #                  'nav2_msgs/srv/ManageLifecycleNodes', '{command: 0}'],
                #             output='screen'
                #         )
                #     ]
                # ),

                # TimerAction(
                #     period=3.0,  # Robot Localization激活后1秒激活AMCL和室内EKF
                #     actions=[
                #         LogInfo(msg="Event-driven: Activating AMCL and Indoor EKF..."),
                #         EmitEvent(event=ChangeState(
                #             lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_amcl_indoor',
                #             transition_id=Transition.TRANSITION_ACTIVATE
                #         ))
                #     ]
                # ),
            ]
        )
    )

    # ============================================================================
    # 返回Launch描述
    # ============================================================================

    return LaunchDescription([
        # 参数声明
        declare_use_sim_time_cmd,

        # 环境变量设置
        SetEnvironmentVariable('RCUTILS_LOGGING_BUFFERED_STREAM', '1'),
        SetEnvironmentVariable('RCUTILS_LOGGING_USE_STDOUT', '1'),
        AppendEnvironmentVariable('GZ_SIM_RESOURCE_PATH', os.path.join(sim_dir, 'models')),
        AppendEnvironmentVariable('GZ_SIM_RESOURCE_PATH', str(Path(os.path.join(sim_dir)).parent.resolve())),

        # 基础设施
        gazebo_server,

        # 事件处理器
        start_robot_after_gazebo,
        start_localization_components_after_robot,

        # 传感器监控事件处理器
        configure_managers_on_sensors_ready,     # 传感器监控启动
        configure_managers_when_sensors_ready,   # 传感器就绪时配置管理器
    ])


if __name__ == '__main__':
    generate_launch_description()
