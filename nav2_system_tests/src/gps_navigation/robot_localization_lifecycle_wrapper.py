#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
生命周期包装器节点，用于管理robot_localization包中的非生命周期节点。

这个包装器节点是一个生命周期节点，可以被lifecycle_manager管理，
它负责启动和停止robot_localization包中的EKF和navsat_transform节点。
"""

import os
import subprocess
import signal
from typing import Dict

import rclpy
from rclpy.lifecycle import LifecycleNode, State, TransitionCallbackReturn


class RobotLocalizationLifecycleWrapper(LifecycleNode):
    """
    robot_localization节点的生命周期包装器。

    这个节点作为生命周期节点，可以被Nav2的lifecycle_manager管理，
    负责启动和停止robot_localization包中的非生命周期节点。
    """

    def __init__(self):
        super().__init__('robot_localization_lifecycle_wrapper')

        # 存储子进程的字典
        self.processes: Dict[str, subprocess.Popen] = {}

        # Bond相关变量
        self.bond = None

        # 声明参数（检查是否已存在）
        try:
            self.declare_parameter('params_file', '')
        except:
            pass  # 参数已存在
        try:
            self.declare_parameter('use_sim_time', True)
        except:
            pass  # 参数已存在
        try:
            self.declare_parameter('enable_tf_publishing', True)  # 控制是否启用TF发布
        except:
            pass  # 参数已存在

        # 定义要管理的节点配置
        self.node_configs = {
            'ekf_filter_node_odom': {
                'package': 'robot_localization',
                'executable': 'ekf_node',
                'name': 'ekf_filter_node_odom',
                'remappings': [('odometry/filtered', 'odometry/local')]
            },
            'ekf_filter_node_map': {
                'package': 'robot_localization',
                'executable': 'ekf_node',
                'name': 'ekf_filter_node_map',
                'remappings': [('odometry/filtered', 'odometry/global')]
            },
            'navsat_transform': {
                'package': 'robot_localization',
                'executable': 'navsat_transform_node',
                'name': 'navsat_transform',
                'remappings': [
                    ('imu/data', 'imu/data'),
                    ('gps/fix', 'gps/fix'),
                    ('gps/filtered', 'gps/filtered'),
                    ('odometry/gps', 'odometry/gps'),
                    ('odometry/filtered', 'odometry/global')
                ]
            }
        }

        self.get_logger().info('Robot localization lifecycle wrapper initialized')

    def on_configure(self, state: State) -> TransitionCallbackReturn:
        """配置状态回调"""
        self.get_logger().info('Configuring robot localization wrapper...')

        # 获取参数
        self.params_file = self.get_parameter('params_file').get_parameter_value().string_value
        self.use_sim_time = self.get_parameter('use_sim_time').get_parameter_value().bool_value
        self.enable_tf_publishing = self.get_parameter('enable_tf_publishing').get_parameter_value().bool_value

        if not self.params_file:
            self.get_logger().error('params_file parameter is required')
            return TransitionCallbackReturn.FAILURE

        if not os.path.exists(self.params_file):
            self.get_logger().error(f'Parameters file does not exist: {self.params_file}')
            return TransitionCallbackReturn.FAILURE

        self.get_logger().info(f'Robot localization wrapper configured successfully, TF publishing: {self.enable_tf_publishing}')
        return TransitionCallbackReturn.SUCCESS

    def on_activate(self, state: State) -> TransitionCallbackReturn:
        """激活状态回调 - 启动robot_localization节点"""
        self.get_logger().info('Activating robot localization wrapper...')

        try:
            for node_name, config in self.node_configs.items():
                self._start_node(node_name, config)

            # 创建bond连接到lifecycle manager
            self._create_bond()

            self.get_logger().info('All robot localization nodes started successfully')
            return TransitionCallbackReturn.SUCCESS

        except Exception as e:
            self.get_logger().error(f'Failed to start robot localization nodes: {str(e)}')
            self._stop_all_nodes()
            return TransitionCallbackReturn.FAILURE

    def on_deactivate(self, state: State) -> TransitionCallbackReturn:
        """停用状态回调 - 停止robot_localization节点"""
        self.get_logger().info('Deactivating robot localization wrapper...')

        self._destroy_bond()
        self._stop_all_nodes()

        self.get_logger().info('Robot localization wrapper deactivated')
        return TransitionCallbackReturn.SUCCESS

    def on_cleanup(self, state: State) -> TransitionCallbackReturn:
        """清理状态回调"""
        self.get_logger().info('Cleaning up robot localization wrapper...')

        # 确保所有进程都已停止
        self._stop_all_nodes()

        self.get_logger().info('Robot localization wrapper cleaned up')
        return TransitionCallbackReturn.SUCCESS

    def on_shutdown(self, state: State) -> TransitionCallbackReturn:
        """关闭状态回调"""
        self.get_logger().info('Shutting down robot localization wrapper...')

        self._stop_all_nodes()

        self.get_logger().info('Robot localization wrapper shut down')
        return TransitionCallbackReturn.SUCCESS

    def _start_node(self, node_name: str, config: dict) -> None:
        """启动单个节点"""
        if node_name in self.processes:
            self.get_logger().warning(f'Node {node_name} is already running')
            return

        # 构建ros2 run命令
        cmd = [
            'ros2', 'run',
            config['package'],
            config['executable'],
            '--ros-args',
            '--params-file', self.params_file,
            '-p', f'use_sim_time:={self.use_sim_time}',
            '-r', f'__node:={config["name"]}'
        ]

        # 根据enable_tf_publishing参数动态设置TF发布参数
        if node_name in ['ekf_filter_node_odom', 'ekf_filter_node_map']:
            cmd.extend(['-p', f'publish_tf:={self.enable_tf_publishing}'])
            self.get_logger().info(f'Setting {node_name} publish_tf to {self.enable_tf_publishing}')
        elif node_name == 'navsat_transform':
            cmd.extend(['-p', f'broadcast_cartesian_transform:={self.enable_tf_publishing}'])
            self.get_logger().info(f'Setting {node_name} broadcast_cartesian_transform to {self.enable_tf_publishing}')

        # 添加重映射
        for old_topic, new_topic in config.get('remappings', []):
            cmd.extend(['-r', f'{old_topic}:={new_topic}'])

        self.get_logger().info(f'Starting node {node_name} with command: {" ".join(cmd)}')

        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid  # 创建新的进程组
            )
            self.processes[node_name] = process
            self.get_logger().info(f'Node {node_name} started with PID {process.pid}')

        except Exception as e:
            self.get_logger().error(f'Failed to start node {node_name}: {str(e)}')
            raise

    def _stop_all_nodes(self) -> None:
        """停止所有节点"""
        for node_name, process in list(self.processes.items()):
            self._stop_node(node_name, process)
        self.processes.clear()

    def _stop_node(self, node_name: str, process: subprocess.Popen) -> None:
        """停止单个节点"""
        if process.poll() is None:  # 进程仍在运行
            self.get_logger().info(f'Stopping node {node_name} (PID: {process.pid})')
            try:
                # 发送SIGTERM信号给整个进程组
                os.killpg(os.getpgid(process.pid), signal.SIGTERM)

                # 等待进程结束
                try:
                    process.wait(timeout=5.0)
                    self.get_logger().info(f'Node {node_name} stopped gracefully')
                except subprocess.TimeoutExpired:
                    # 如果5秒后还没结束，强制杀死
                    self.get_logger().warning(f'Node {node_name} did not stop gracefully, force killing')
                    os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                    process.wait()

            except ProcessLookupError:
                # 进程已经不存在
                self.get_logger().info(f'Node {node_name} process already terminated')
            except Exception as e:
                self.get_logger().error(f'Error stopping node {node_name}: {str(e)}')
        else:
            self.get_logger().info(f'Node {node_name} was already stopped')

    def _create_bond(self) -> None:
        """创建与lifecycle manager的bond连接"""
        # 对于简单的实现，我们不需要显式创建bond
        # ROS2的生命周期节点会自动处理bond连接
        self.get_logger().info('Node is ready for bond connection from lifecycle manager')

    def _destroy_bond(self) -> None:
        """销毁bond连接"""
        # 清理资源
        self.get_logger().info('Cleaning up bond resources')


def main(args=None):
    """主函数"""
    rclpy.init(args=args)

    try:
        node = RobotLocalizationLifecycleWrapper()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'Error: {e}')
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
